package com.tekup.rh_management.services;
import org.springframework.stereotype.Service;

import com.tekup.rh_management.dto.AuthenticationRequest;
import com.tekup.rh_management.dto.AuthenticationResponse;
import com.tekup.rh_management.dto.RegisterRequest;
import com.tekup.rh_management.dto.RegisterResponse;
import com.tekup.rh_management.dto.UsernameCheckResponse;


public interface AuthenticationService {
    RegisterResponse register(RegisterRequest request);
    AuthenticationResponse authenticate(AuthenticationRequest request);
    UsernameCheckResponse checkUsernameAvailability(String username);
} 